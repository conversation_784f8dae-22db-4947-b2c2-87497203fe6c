"""Database models module.

This module contains all SQLModel classes for the BPO Admin application.
All models include standardized created_at and updated_at timestamp fields.
"""

from datetime import datetime
from typing import Optional

from sqlalchemy import Column, DateTime, func, JSON
from sqlalchemy.ext.mutable import MutableList
from sqlmodel import BigInteger, Field, SQLModel, Text


class User(SQLModel, table=True):
    """SQLModel class for storing user information in the database.

    Attributes:
        id: Primary key for the user
        name: User's display name
        email: User's email address
        password: User's hashed password
        is_active: Whether the user account is active
        is_superuser: Whether the user has admin privileges
        created_at: Timestamp when user was created
        updated_at: Timestamp when user was last updated
        role_id: Role ID for the user
    """

    __tablename__: str = "users"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    name: Optional[str] = Field(max_length=20)
    phone: Optional[str] = Field(max_length=20)
    pic: Optional[str] = Field(default=None, max_length=200)
    email: Optional[str] = Field(max_length=50)
    password: Optional[str] = Field(max_length=100)
    is_active: bool = Field(default=True)
    is_superuser: bool = Field(default=False)
    role_id: Optional[int] = Field(default=None)
    department: Optional[str] = Field(default=None)
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )


class TalentProfile(SQLModel, table=True):
    """SQLModel class for storing talent profile information in the database.

    Attributes:
        id: Primary key for the talent profile
        created_at: Timestamp when talent profile was created
        updated_at: Timestamp when talent profile was last updated
        hire_date: Date when the talent was hired
        status: Whether the talent profile is active
        full_time_employee: Whether the talent is a full-time employee
        other_employee: Additional information about the talent (e.g., part-time, contractor)
        contract_period: Contract period of the talent
        contract_end_date: End date of the talent's contract
        first_name: Talent's first name
        middle_name: Talent's middle name
        last_name: Talent's last name
        email: Talent's email address
        company_email: Talent's company email address
        phone: Talent's contact phone number
        phone2: Talent's alternate contact phone number
        dob: Date of birth of the talent
        address_1: Talent's primary address
        address_2: Talent's secondary address
        city: City where the talent is located
        state: State where the talent is located
        country: Country where the talent is located
        zip_code: Zip code of the talent's location
        pic: Profile picture of the talent
    """

    __tablename__: str = "talent_profiles"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    user_agent: Optional[str] = Field(
        description="user agent", title="user agent", default=None
    )
    ip_address: Optional[str] = Field(
        description="ip address", title="ip address", default=None
    )
    hire_date: Optional[datetime] = Field(description="hire date", title="hire date")
    full_time_employee: Optional[bool] = Field(
        description="full time employee", title="full time employee"
    )
    other_employee: Optional[str] = Field(
        description="other employee", title="other employee", default=None
    )
    first_name: str = Field(max_length=20)
    middle_name: Optional[str] = Field(
        description="middle name", title="middle name", default=None
    )
    last_name: str = Field(max_length=20)
    gender: str = Field(max_length=20)
    personal_email: str = Field(max_length=50)
    country_code: Optional[str] = Field(
        description="country code", title="country code", default=None, max_length=10
    )
    phone: str = Field(max_length=10)
    country_code2: Optional[str] = Field(
        description="country code 2", title="country code 2", default=None, max_length=10
    )
    phone2: Optional[str] = Field(
        description="phone 2", title="phone 2", default=None, max_length=10
    )
    date_of_birth: Optional[datetime] = Field(
        description="date of birth", title="date of birth", default=None
    )
    address_1: str = Field(max_length=100)
    address_2: Optional[str] = Field(
        description="address 2", title="address 2", default=None
    )
    apt_unit: Optional[str] = Field(max_length=20)
    city: str = Field(max_length=20)
    state: str = Field(max_length=20)
    country: str = Field(max_length=20)
    zip_code: str = Field(max_length=10)
    pic: Optional[str] = Field(max_length=200)
    deactivation_reason: Optional[str] = Field(
        description="deactivation reason", title="deactivation reason", default=None
    )
    is_active: bool = Field(default=True)


class TalentCronicConditions(SQLModel, table=True):
    """SQLModel class for storing talent profile cronic conditions information in the database.

    Attributes:
        id: Primary key for the talent profile cronic conditions
        created_at: Timestamp when talent profile cronic conditions was created
        updated_at: Timestamp when talent profile cronic conditions was last updated
        talent_profile_id: Foreign key reference to the associated talent profile
        cronic_condition: Cronic condition of the talent
        medication: Medication of the talent
    """

    __tablename__: str = "talent_cronic_conditions"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    talent_profile_id: int = Field(
        foreign_key="talent_profiles.id", ondelete="CASCADE", sa_type=BigInteger
    )
    cronic_condition: Optional[str] = Field(
        default=None, description="cronic condition", title="cronic condition"
    )
    medication: Optional[str] = Field(
        default=None, description="medication", title="medication"
    )


class TalentPastHealthIssues(SQLModel, table=True):
    """SQLModel class for storing talent past health issues information in the database.

    Attributes:
        id: Primary key for the talent past health issues information
        created_at: Timestamp when talent past health issues information was created
        updated_at: Timestamp when talent past health issues information was last updated
        talent_profile_id: Foreign key reference to the associated talent profile
        past_health_issues: Past health issues of the talent
    """

    __tablename__: str = "talent_past_health_issues"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    talent_profile_id: int = Field(
        foreign_key="talent_profiles.id", ondelete="CASCADE", sa_type=BigInteger
    )
    past_health_issues: str = Field(
        default=None, description="past health issues", title="past health issues"
    )


class TalentOngoingHealthIssues(SQLModel, table=True):
    """SQLModel class for storing talent ongoing health issues information in the database.

    Attributes:
        id: Primary key for the talent ongoing health issues information
        created_at: Timestamp when talent ongoing health issues information was created
        updated_at: Timestamp when talent ongoing health issues information was last updated
        talent_profile_id: Foreign key reference to the associated talent profile
        ongoing_health_issues: Ongoing health issues of the talent
    """

    __tablename__: str = "talent_ongoing_health_issues"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    talent_profile_id: int = Field(
        foreign_key="talent_profiles.id", ondelete="CASCADE", sa_type=BigInteger
    )
    ongoing_health_issues: Optional[str] = Field(
        default=None, description="ongoing health issues", title="ongoing health issues"
    )
    medication: Optional[str] = Field(
        default=None, description="medication", title="medication"
    )


class TalentAllergies(SQLModel, table=True):
    """SQLModel class for storing talent allergies information in the database.

    Attributes:
        id: Primary key for the talent allergies information
        created_at: Timestamp when talent allergies information was created
        updated_at: Timestamp when talent allergies information was last updated
        talent_profile_id: Foreign key reference to the associated talent profile
        allergy: Allergies of the talent
        medication: Medication of the talent
    """

    __tablename__: str = "talent_allergies"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    talent_profile_id: int = Field(
        foreign_key="talent_profiles.id", ondelete="CASCADE", sa_type=BigInteger
    )
    allergy: str = Field(default=None, description="allergy", title="allergy")
    medication: Optional[str] = Field(
        default=None, description="medication", title="medication"
    )


class TalentDocumentsCollected(SQLModel, table=True):
    """SQLModel class for storing talent documents information in the database.

    Attributes:
        id: Primary key for the talent documents information
        created_at: Timestamp when talent documents information was created
        updated_at: Timestamp when talent documents information was last updated
        talent_profile_id: Foreign key reference to the associated talent profile
        doc_type: Personal Document type
        url: Personal Document Front url

    """

    __tablename__: str = "talent_documents_collected"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    talent_profile_id: int = Field(
        foreign_key="talent_profiles.id", ondelete="CASCADE", sa_type=BigInteger
    )
    doc_type: str = Field(description="type", title="type", max_length=100)
    url: str = Field(description="url", title="url", max_length=250)

class TalentSkillSetMapping(SQLModel, table=True):
    """SQLModel class for storing talent profile skill information in the database.

    Attributes:
        id: Primary key for the talent profile skill
        created_at: Timestamp when talent profile skill was created
        updated_at: Timestamp when talent profile skill was last updated
        talent_profile_id: Foreign key reference to the associated talent profile
        skills: Array of skills talent has
        years_of_experience: Years of experience in the skill
        notes_from_interview: Notes from the talent interview
        english_level: English level of the talent as a dictionary with 'write', 'speak', and 'type' keys
    """

    __tablename__: str = "talent_skill_set_mapping"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    talent_profile_id: int = Field(
        foreign_key="talent_profiles.id", ondelete="CASCADE", sa_type=BigInteger
    )
    skills: Optional[list[str]] = Field(
        description="skills",
        title="skills",
        sa_column=Column(MutableList.as_mutable(JSON)),
    )
    years_of_experience: Optional[float] = Field(
        description="years of experience", title="years of experience"
    )
    notes_from_interview: Optional[str] = Field(
        description="notes from interview",
        title="notes from interview",
        default=None,
        sa_column=Column(Text),
    )
    english_level: Optional[dict[str, str]] = Field(
        description="english level",
        title="english level",
        sa_column=Column(JSON),
    )


class TalentPayrollInformationMapping(SQLModel, table=True):
    """SQLModel class for storing talent payroll information in the database.

    Attributes:
        id: Primary key for the talent payroll information
        created_at: Timestamp when talent payroll information was created
        updated_at: Timestamp when talent payroll information was last updated
        talent_profile_id: Foreign key reference to the associated talent profile
        imms: IMMS number of the talent
        curp: CURP of the talent
        rfc: RFC of the talent
    """

    __tablename__: str = "talent_payroll_information_mapping"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    talent_profile_id: int = Field(
        foreign_key="talent_profiles.id", ondelete="CASCADE", sa_type=BigInteger
    )
    imms: Optional[str] = Field(max_length=100, nullable=True)
    curp: Optional[str] = Field(max_length=100, nullable=True)
    rfc: Optional[str] = Field(max_length=100, nullable=True)


class TalentBankingInformationMapping(SQLModel, table=True):
    """SQLModel class for storing banking information in the database.

    Attributes:
        id: Primary key for the banking information
        created_at: Timestamp when banking information was created
        updated_at: Timestamp when banking information was last updated
        talent_profile_id: Foreign key reference to the associated talent profile
        bank_name: Name of the bank
        account_number: Account number of the talent
        clabe: CLABE of the talent
        swift_code: Swift code of the talent
    """

    __tablename__: str = "talent_banking_information_mapping"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    talent_profile_id: int = Field(
        foreign_key="talent_profiles.id", ondelete="CASCADE", sa_type=BigInteger
    )
    bank_name: str = Field(max_length=100, nullable=True)
    account_number: str = Field(max_length=50, nullable=True)
    clabe: str = Field(max_length=100, nullable=True)
    swift_code: Optional[str] = Field(max_length=100, nullable=True)


class TalentEmergencyContactMapping(SQLModel, table=True):
    """SQLModel class for storing emergency contact information in the database.

    Attributes:
        id: Primary key for the emergency contact information
        created_at: Timestamp when emergency contact information was created
        updated_at: Timestamp when emergency contact information was last updated
        talent_profile_id: Foreign key reference to the associated talent profile
        name: Name of the emergency contact
        relationship: Relationship of the emergency contact
        phone: Phone number of the emergency contact
        notes: Note about the emergency contact
    """

    __tablename__: str = "talent_emergency_contact_mapping"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    talent_profile_id: int = Field(
        foreign_key="talent_profiles.id", ondelete="CASCADE", sa_type=BigInteger
    )
    first_name: str = Field(max_length=20, nullable=True)
    middle_name: Optional[str] = Field(max_length=20, nullable=True)
    last_name: Optional[str] = Field(max_length=20, nullable=True)
    relationship: str = Field(max_length=50, nullable=True)
    phone: str = Field(max_length=20, nullable=True)
    country_code: str = Field(max_length=5, nullable=True)
    email: Optional[str] = Field(max_length=50, nullable=True)

class MasterClient(SQLModel, table=True):
    """SQLModel class for storing master client information in the database.

    Attributes:
        id: Primary key for the client information
        name: Client name
        is_active: Client active status
    """
    
    __tablename__: str = "master_clients"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    is_active: bool = Field(default=True)
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    name: str = Field(max_length=200, description="Client name")
    client_id: str = Field(max_length=200, description="Client ID reference")
    start_date: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    end_date: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, nullable=True)
    )
    street_address: Optional[str] = Field(max_length=200, nullable=True, description="Client street address")
    city: Optional[str] = Field(max_length=200, nullable=True, description="Client city")
    country: Optional[str] = Field(max_length=200, nullable=True, description="Client country")
    zip_code: Optional[str] = Field(max_length=200, nullable=True, description="Client zip code")
    contact_manager: Optional[str] = Field(max_length=200, nullable=True, description="Client contact manager")
    contact_manager_email: Optional[str] = Field(max_length=200, nullable=True, description="Client contact manager email")
    contact_manager_phone: Optional[str] = Field(max_length=200, nullable=True, description="Client contact manager phone")
    notes: Optional[str] = Field(max_length=200, nullable=True, description="Client notes")


class DocumentClientMapping(SQLModel, table=True):
    """SQLModel class for storing document client mapping in the database.



        id: Primary key for the document client mapping
        created_at: Timestamp when document client mapping was created
        updated_at: Timestamp when document client mapping was last updated
        document_id: Foreign key reference to the associated document
        client_id: Foreign key reference to the associated client
    """
    
    __tablename__: str = "document_client_mapping"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    url: Optional[str] = Field(max_length=200, nullable=True)
    client_id: int = Field(
        foreign_key="master_clients.id", ondelete="CASCADE", sa_type=BigInteger
    )


class TalentClientInfo(SQLModel, table=True):
    """SQLModel class for storing talent client information in the database.

    Attributes:
        id: Primary key for the talent client information
        created_at: Timestamp when talent client information was created
        updated_at: Timestamp when talent client information was last updated
        talent_profile_id: Foreign key reference to the associated talent profile
        client: Client of the talent
        bpo_manager: BPO manager of the talent
        client_id: Client id of the talent
        reporting_department: Reporting department of the talent
    """

    __tablename__: str = "talent_client_info"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    talent_profile_id: int = Field(
        foreign_key="talent_profiles.id", ondelete="CASCADE", sa_type=BigInteger
    )
    role: Optional[str] = Field(max_length=20, nullable=True)
    title: Optional[str] = Field(description="title", title="title", nullable=True)
    client_id: int = Field(
        foreign_key="master_clients.id", ondelete="CASCADE", sa_type=BigInteger
    )
    location: Optional[str] = Field(description="location", title="location", nullable=True)
    start_date: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    end_date: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, nullable=True)
    )
    is_fte: Optional[bool] = Field(description="is fte", title="is fte", nullable=True)
    is_contract: Optional[bool] = Field(description="is contract", title="is contract", nullable=True)
    is_other: Optional[bool] = Field(description="is other", title="is other", nullable=True) 
    work_days: Optional[list[str]] = Field(title="working week days", sa_column=Column(MutableList.as_mutable(JSON)))
    shift_start_time: Optional[str] = Field(description="shift start time", title="shift start time", nullable=True)
    shift_end_time: Optional[str] = Field(description="shift end time", title="shift end time", nullable=True)
    time_zone: Optional[str] = Field(description="time zone", title="time zone", nullable=True)
    campaign: Optional[str] = Field(description="campaign", title="campaign", nullable=True)
    client_manager: Optional[str] = Field(description="bpo manager", title="bpo manager", nullable=True)
    reporting_department: Optional[str] = Field(
        description="bpo department", title="bpo department", nullable=True
    )
    is_active: bool = Field(description="is active", title="is active", default=True)

class TalentPositionMapping(SQLModel, table=True):
    """SQLModel class for storing talent position information in the database.

    Attributes:
        id: Primary key for the talent position information
        created_at: Timestamp when talent position information was created
        updated_at: Timestamp when talent position information was last updated
        talent_profile_id: Foreign key reference to the associated talent profile
        position: Position of the talent
        contract_period: Contract period of the talent (Optional)
        contract_end_date: Contract end date of the talent (Optional)
        role: Role of the talent
        title: Title of the talent position
        client_placed: Client placed of the talent
        time: Working hours of the talent
        start_date: Start date of the talent position
        end_date: End date of the talent position
        termination_date: Termination date of the talent
        reason_for_termination: Reason for termination of the talent
        notes_for_termination: Notes for termination of the talent
        salary: Salary of the talent (Optional)
        hours: Working hours of the talent (Optional)
        hourly_rate: Hourly rate of the talent (Optional)
    """

    __tablename__: str = "talent_position_mapping"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    talent_profile_id: int = Field(
        foreign_key="talent_profiles.id", ondelete="CASCADE", sa_type=BigInteger
    )
    reporting_manager: Optional[str] = Field(max_length=100, nullable=True)
    reporting_department: Optional[str] = Field(max_length=100, nullable=True)
    campaign: Optional[str] = Field(max_length=100, nullable=True)
    bpo_email: Optional[str] = Field(max_length=100, nullable=True)
    position: Optional[str] = Field(max_length=100, nullable=True)
    role: Optional[str] = Field(max_length=20, nullable=True)
    title: Optional[str] = Field(max_length=20, nullable=True)
    shift_start_time: Optional[str] = Field(description="shift start time", title="shift start time", nullable=True)
    shift_end_time: Optional[str] = Field(description="shift end time", title="shift end time", nullable=True)
    start_date: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, nullable=True)
    )
    contract_end_date: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, nullable=True)
    )
    termination_date: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, nullable=True)
    )
    location: Optional[str] = Field(max_length=20, nullable=True)
    location_duration: Optional[str] = Field(max_length=20, nullable=True)
    reason_for_termination: Optional[str] = Field(max_length=20, nullable=True)
    hours_per_week: Optional[str] = Field(max_length=20, nullable=True)
    hourly_rate: Optional[str] = Field(max_length=20, nullable=True)
    working_week_days: Optional[list[str]] = Field(title="working week days", sa_column=Column(MutableList.as_mutable(JSON)))
    wage_base: Optional[str] = Field(max_length=20, nullable=True)
    wage_currency: Optional[str] = Field(max_length=20, nullable=True)
    wage_frequency: Optional[str] = Field(max_length=20, nullable=True)
    notes: Optional[str] = Field(max_length=500, nullable=True)


class TalentWageHistory(SQLModel, table=True):
    """SQLModel class for storing talent wage history information in the database.

    Attributes:
        id: Primary key for the talent wage history information
        created_at: Timestamp when talent wage history information was created
        updated_at: Timestamp when talent wage history information was last updated
        wage_information_id: Foreign key reference to the associated talent wage information
        period: Period of the wage history
        start_date: Start date of the wage history
        end_date: End date of the wage history
        amount: Amount of the wage history
        worked_days: Worked days of the wage history
        worked_hours: Worked hours of the wage history
    """

    __tablename__: str = "talent_wage_history"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    period: str = Field(description="period", title="period")
    wage_base: Optional[str] = Field(max_length=20, nullable=True)
    wage_currency: Optional[str] = Field(max_length=20, nullable=True)
    wage_frequency: Optional[str] = Field(max_length=20, nullable=True)
    shift_start_time: Optional[str] = Field(description="shift start time", title="shift start time", nullable=True)
    shift_end_time: Optional[str] = Field(description="shift end time", title="shift end time", nullable=True)
    start_date: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, nullable=True)
    )
    end_date: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, nullable=True)
    )
    worked_days: int = Field(description="worked days", title="worked days")
    worked_hours: float = Field(description="worked hours", title="worked hours")
    client_id: int = Field(
        description="client id",
        title="client id",
        nullable=True,
        foreign_key="master_clients.id",
        ondelete="CASCADE",
        sa_type=BigInteger,
    )


class TalentVacationMapping(SQLModel, table=True):
    """SQLModel class for storing talent vacation information in the database.

    Attributes:
        id: Primary key for the vacation information
        created_at: Timestamp when vacation information was created
        updated_at: Timestamp when vacation information was last updated
        talent_profile_id: Foreign key reference to the associated talent profile
        year_number: Year number since start date
        available_bereavement_days: Available bereavement days for the year
        remaining_bereavement_days: Remaining bereavement days for the year
        available_vacation_days: Available vacation days for the year
        remaining_vacation_days: Remaining vacation days for the year
        available_parental_days: Available paid days for the year
        remaining_parental_days: Remaining paid days for the year
    """

    __tablename__: str = "talent_vacation_mapping"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    talent_profile_id: int = Field(
        foreign_key="talent_profiles.id", ondelete="CASCADE", sa_type=BigInteger
    )
    year_number: int = Field(description="year number", title="year number")
    accrued_paid_time_off_days: Optional[float] = Field(
        description="accrued paid time", title="accrued paid time", default=None
    )
    used_paid_time_off_days: Optional[float] = Field(
        description="used paid time", title="used paid time", default=None
    )
    available_paid_time_off_days: Optional[float] = Field(
        description="available paid time off days", title="available paid time off days", default=None
    )
    available_bereavement_days: Optional[float] = Field(
        description="available bereavement days", title="available bereavement days", default=None
    )
    remaining_bereavement_days: Optional[float] = Field(
        description="remaining bereavement days", title="remaining bereavement days", default=None
    )
    available_vacation_days: Optional[int] = Field(
        description="available vacation days", title="available vacation days", default=None
    )
    used_vacation_days: Optional[int] = Field(
        description="used vacation days", title="used vacation days", default=None
    )
    remaining_vacation_days: Optional[int] = Field(
        description="remaining vacation days", title="remaining vacation days", default=None
    )
    available_parental_days: Optional[float] = Field(
        description="available parental days", title="available parental days", default=None
    )
    remaining_parental_days: Optional[float] = Field(
        description="remaining parental days", title="remaining parental days", default=None
    )


class TalentLocationMapping(SQLModel, table=True):
    """SQLModel class for storing talent location mapping information in the database.

    Attributes:
        id: Primary key for the location information
        created_at: Timestamp when location information was created
        updated_at: Timestamp when location information was last updated
        talent_profile_id: Foreign key reference to the associated talent profile
        location: Location information
        location_id: Location id
    """

    __tablename__: str = "talent_location_mapping"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    talent_profile_id: int = Field(
        foreign_key="talent_profiles.id", ondelete="CASCADE", sa_type=BigInteger
    )
    location: str = Field(max_length=20)
    duration_of_stay: str = Field(max_length=20)

class MasterEquipment(SQLModel, table=True):
    """SQLModel class for storing master equipment information in the database.

    Attributes:
        id: Primary key for the equipment information
        name: Equipment name
        model: Equipment model
        serial_number: Equipment serial number
        is_active: Equipment active status
        created_at: Timestamp when equipment information was created
        updated_at: Timestamp when equipment information was last updated
    """

    __tablename__: str = "master_equipment"  # type: ignore
    
    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    name: str = Field(max_length=200)
    equipment_id: Optional[str] = Field(max_length=200, default=None)
    category: Optional[str] = Field(max_length=200, default=None)
    serial_number: Optional[str] = Field(max_length=200, default=None)
    model: Optional[str] = Field(max_length=200, default=None)
    purchase_date: Optional[datetime] = Field(max_length=200, default=None)
    is_occupied: bool = Field(default=False)
    is_active: bool = Field(default=True)

class TalentEquipmentMapping(SQLModel, table=True):
    """SQLModel class for storing talent equipment mapping information in the database.

    Attributes:
        id: Primary key for the equipment mapping information
        created_at: Timestamp when equipment mapping information was created
        updated_at: Timestamp when equipment information was last updated
        talent_profile_id: Foreign key reference to the associated talent profile
        equipment: Equipment information
        equipment_id: Equipment id
    """

    __tablename__: str = "talent_equipment_mapping"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    talent_profile_id: int = Field(
        foreign_key="talent_profiles.id", ondelete="CASCADE", sa_type=BigInteger
    )
    master_equipment_id: int = Field(
        foreign_key="master_equipment.id", ondelete="CASCADE", sa_type=BigInteger
    )
    is_active: bool = Field(default=True)


class TalentSoftwareMapping(SQLModel, table=True):
    """SQLModel class for storing talent software mapping information in the database.

    Attributes:
        id: Primary key for the software information
        created_at: Timestamp when software information was created
        updated_at: Timestamp when software information was last updated
        talent_profile_id: Foreign key reference to the associated talent profile
        software: Software information
        software_id: Software id
        software_version: Software version
        software_key: Software key
    """

    __tablename__: str = "talent_software_mapping"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    talent_profile_id: int = Field(
        foreign_key="talent_profiles.id", ondelete="CASCADE", sa_type=BigInteger
    )
    software: str = Field(max_length=20)
    software_version: Optional[str] = Field(max_length=20, nullable=True)
    software_key: Optional[str] = Field(max_length=20, nullable=True)
    status: Optional[bool] = Field(nullable=True)


class TalentITDocumentsMapping(SQLModel, table=True):
    """SQLModel class for storing talent IT documents mapping information in the database.

    Attributes:
        id: Primary key for the IT documents mapping information
        created_at: Timestamp when IT documents mapping information was created
        updated_at: Timestamp when IT documents mapping information was last updated
        talent_profile_id: Foreign key reference to the associated talent profile
        document_type: IT document type
        url: IT document url
        notes: IT document description
    """

    __tablename__: str = "talent_it_documents_mapping"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    talent_profile_id: int = Field(
        foreign_key="talent_profiles.id", ondelete="CASCADE", sa_type=BigInteger
    )
    document_type: str = Field(max_length=200)
    url: str = Field(max_length=250)
    notes: Optional[str] = Field(max_length=200, nullable=True)

class MasterRole(SQLModel, table=True):
    """SQLModel class for storing role information in the database.

    Attributes:
        id: Primary key for the role information
        created_at: Timestamp when role information was created
        updated_at: Timestamp when role information was last updated
        name: Role name
    """

    __tablename__: str = "master_role"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    name: str = Field(max_length=20)
    description: str = Field(max_length=200)


class MasterModule(SQLModel, table=True):
    """SQLModel class for storing module information in the database.

    Attributes:
        id: Primary key for the module information
        created_at: Timestamp when module information was created
        updated_at: Timestamp when module information was last updated
        name: Module name
        is_active: Module status
    """

    __tablename__: str = "master_module"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    name: str = Field(max_length=50, nullable=True)
    is_active: bool = Field(default=True)


class RoleModulePermissionMapping(SQLModel, table=True):
    """SQLModel class for storing role permission mapping information in the database.

    Attributes:
        id: Primary key for the role permission mapping information
        created_at: Timestamp when role permission mapping information was created
        updated_at: Timestamp when role permission mapping information was last updated
        role_id: Foreign key reference to the associated role
        permission_id: Foreign key reference to the associated permission
    """

    __tablename__: str = "role_permission_mapping"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    role_id: int = Field(
        foreign_key="master_role.id", ondelete="CASCADE", sa_type=BigInteger
    )
    module_id: int = Field(
        foreign_key="master_module.id", ondelete="CASCADE", sa_type=BigInteger
    )
    can_list: bool = Field(default=False)
    can_create: bool = Field(default=False)
    can_update: bool = Field(default=False)
    can_view: bool = Field(default=False)


class UserActivity(SQLModel, table=True):
    """SQLModel class for storing user activity information in the database.

    Attributes:
        id: Primary key for the user activity information
        created_at: Timestamp when user activity information was created
        updated_at: Timestamp when user activity information was last updated
        user_id: Foreign key reference to the associated user
        activity_type: Type of user activity
        activity: User activity
        ip_address: IP address of the user
        user_agent: User agent of the user
        browser: Browser used by the user
        os: Operating system used by the user
        device: Device used by the user
        status: Status of the user activity
    """

    __tablename__: str = "user_activity"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    user_id: int = Field(
        foreign_key="users.id", ondelete="CASCADE", sa_type=BigInteger
    )
    activity_type: str = Field(max_length=20)
    activity: str = Field(max_length=200)
    ip_address: Optional[str] = Field(max_length=20, nullable=True)
    user_agent: Optional[str] = Field(max_length=200, nullable=True)
    browser: Optional[str] = Field(max_length=20, nullable=True)
    os: Optional[str] = Field(max_length=20, nullable=True)
    device: Optional[str] = Field(max_length=20, nullable=True)
    status: Optional[bool] = Field(nullable=True)

class TalentActivity(SQLModel, table=True):
    """SQLModel class for storing talent activity information in the database.

    Attributes:
        id: Primary key for the talent activity information
        created_at: Timestamp when talent activity information was created
        updated_at: Timestamp when talent activity information was last updated
        talent_profile_id: Foreign key reference to the associated talent profile
        activity_type: Type of talent activity
        activity: Talent activity
        ip_address: IP address of the user
        user_agent: User agent of the user
        browser: Browser used by the user
        os: Operating system used by the user
        device: Device used by the user
        status: Status of the talent activity
    """

    __tablename__: str = "talent_activity"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    talent_profile_id: Optional[int] = Field(
        default=None,
        foreign_key="talent_profiles.id",
        ondelete="CASCADE",
        sa_type=BigInteger,
        nullable=True,
    )
    user_id: int = Field(
        foreign_key="users.id", ondelete="CASCADE", sa_type=BigInteger
    )
    activity_type: str = Field(max_length=20)
    activity: str = Field(max_length=200)
    old_data: Optional[str] = Field(default=None, sa_column=Column(Text, nullable=True))
    new_data: Optional[str] = Field(default=None, sa_column=Column(Text, nullable=True))
    ip_address: Optional[str] = Field(max_length=20, nullable=True)
    user_agent: Optional[str] = Field(max_length=200, nullable=True)
    browser: Optional[str] = Field(max_length=20, nullable=True)
    os: Optional[str] = Field(max_length=20, nullable=True)
    device: Optional[str] = Field(max_length=20, nullable=True)
    status: Optional[bool] = Field(nullable=True)
