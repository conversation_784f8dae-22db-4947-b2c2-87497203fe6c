"""Activity service for talent management.

This module contains the service class for talent activity business logic operations.
"""

import json
from typing import Optional, Dict, Any, List
from app.repositories.master.role_repository import RoleRepository
from app.repositories.talent.activity_repository import TalentActivityRepository
from app.repositories.user_repository import UserRepository
from app.schemas.talent.activity_schema import (
    TalentActivityResponse, 
    TalentActivityCreate, 
    ActivityLogResponse, 
    ActivityDescription
)
from app.response_models.general_response import GeneralResponse
from app.core.logs import log_error_with_context
from app.db.models import User


class TalentActivityService:
    """Service class for talent activity business logic operations.
    
    Handles business logic for talent activity retrieval.
    """
    
    def __init__(
        self,
        repository: TalentActivityRepository,
        user_repository: UserRepository,
        role_repository: RoleRepository,
        current_user: Optional[User] = None
    ):
        """Initialize the service with repository and user dependencies.
        
        Args:
            repository: Activity repository instance
            user_repository: User repository instance
            role_repository: Role repository instance
            current_user: Current authenticated user
        """
        self.repository = repository
        self.user_repository = user_repository
        self.role_repository = role_repository
        self.current_user = current_user
    
    def create_activity(
        self,
        talent_profile_id: Optional[int] = None,
        activity_type: str = '',
        activity: str = '',
        old_data: Optional[Dict[str, Any]] = None,
        new_data: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        browser: Optional[str] = None,
        os: Optional[str] = None,
        device: Optional[str] = None
    ):
        """Create a new talent activity record.
        
        Args:
            talent_profile_id: ID of the talent profile
            activity_type: Type of activity (CREATE, UPDATE, DELETE, etc.)
            activity: Description of the activity
            old_data: Previous data state (for updates)
            new_data: New data state
            ip_address: User's IP address
            user_agent: User's browser user agent
            browser: User's browser name
            os: User's operating system
            device: User's device type
            
        Returns:
            GeneralResponse containing the created activity or error
        """
        try:
            # Convert dictionaries to JSON strings for storage
            old_data_json = json.dumps(old_data) if old_data else None
            new_data_json = json.dumps(new_data) if new_data else None
            
            activity_data = TalentActivityCreate(
                talent_profile_id=talent_profile_id,
                user_id=self.current_user.id or 0,
                activity_type=activity_type,
                activity=activity,
                old_data=old_data_json,
                new_data=new_data_json,
                ip_address=ip_address,
                user_agent=user_agent,
                browser=browser,
                os=os,
                device=device,
                status=True
            )
            
            created_activity = self.repository.create(activity_data)
            if not created_activity:
                log_error_with_context(
                    error=Exception("Failed to create talent activity"),
                    context={
                        "talent_profile_id": talent_profile_id,
                        "activity_type": activity_type,
                        "user_id": self.current_user.id if self.current_user else None
                    }
                )
            
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "talent_profile_id": talent_profile_id,
                    "activity_type": activity_type,
                    "user_id": self.current_user.id
                }
            )
    
    def get_activity_by_id(self, activity_id: int) -> GeneralResponse:
        """Get a talent activity by ID.
        
        Args:
            activity_id: Activity ID
            
        Returns:
            GeneralResponse containing the activity or error
        """
        try:
            activity = self.repository.get_by_id(activity_id)
            if not activity:
                log_error_with_context(
                    error=Exception("Talent activity not found"),
                    context={"activity_id": activity_id}
                )
                return GeneralResponse(
                    message="Talent activity not found",
                    status_code=404,
                    data=None
                )
            
            response_data = TalentActivityResponse.model_validate(activity)
            return GeneralResponse(
                message="Talent activity retrieved successfully",
                status_code=200,
                data=response_data
            )
            
        except Exception as e:
            log_error_with_context(
                error=e,
                context={"activity_id": activity_id}
            )
            return GeneralResponse(
                message="An error occurred while retrieving talent activity",
                status_code=500,
                data=None
            )
    
    def get_activities_by_talent_id(
        self,
        talent_profile_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> GeneralResponse:
        """Get all activities for a specific talent with pagination.
        
        Args:
            talent_profile_id: Talent profile ID
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            GeneralResponse containing list of activities or error
        """
        try:
            activities = self.repository.get_by_talent_id(talent_profile_id, skip, limit)
            
            response_data = [
                TalentActivityResponse.model_validate(activity)
                for activity in activities
            ]
            
            return GeneralResponse(
                message=f"Retrieved {len(response_data)} talent activities",
                status_code=200,
                data=response_data
            )
            
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "talent_profile_id": talent_profile_id,
                    "skip": skip,
                    "limit": limit
                }
            )
            return GeneralResponse(
                message="An error occurred while retrieving talent activities",
                status_code=500,
                data=[]
            )
    
    def get_activities_by_user_id(
        self,
        user_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> GeneralResponse:
        """Get all activities performed by a specific user with pagination.
        
        Args:
            user_id: User ID
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            GeneralResponse containing list of activities or error
        """
        try:
            activities = self.repository.get_by_user_id(user_id, skip, limit)
            
            response_data = [
                TalentActivityResponse.model_validate(activity)
                for activity in activities
            ]
            
            return GeneralResponse(
                message=f"Retrieved {len(response_data)} user activities",
                status_code=200,
                data=response_data
            )
            
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "user_id": user_id,
                    "skip": skip,
                    "limit": limit
                }
            )
            return GeneralResponse(
                message="An error occurred while retrieving user activities",
                status_code=500,
                data=[]
            )
    
    def get_activities_by_type(
        self,
        activity_type: str,
        skip: int = 0,
        limit: int = 100
    ) -> GeneralResponse:
        """Get all activities of a specific type with pagination.
        
        Args:
            activity_type: Type of activity to filter by
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            GeneralResponse containing list of activities or error
        """
        try:
            activities = self.repository.get_by_activity_type(activity_type, skip, limit)
            
            response_data = [
                TalentActivityResponse.model_validate(activity)
                for activity in activities
            ]
            
            return GeneralResponse(
                message=f"Retrieved {len(response_data)} activities of type {activity_type}",
                status_code=200,
                data=response_data
            )
            
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "activity_type": activity_type,
                    "skip": skip,
                    "limit": limit
                }
            )
            return GeneralResponse(
                message="An error occurred while retrieving activities by type",
                status_code=500,
                data=[]
            )
    
    def get_formatted_activities_by_talent_id(
        self,
        talent_profile_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> GeneralResponse:
        """Get formatted activity logs for a specific talent.
        
        Args:
            talent_profile_id: Talent profile ID
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            GeneralResponse containing formatted activity logs
        """
        try:
            activities = self.repository.get_by_talent_id(talent_profile_id, skip, limit)
            
            formatted_activities: List[ActivityLogResponse] = []
            for activity in activities:
                # Parse old and new data if they exist
                old_data: Dict[str, Any] = json.loads(activity.old_data) if activity.old_data else {}
                new_data: Dict[str, Any] = json.loads(activity.new_data) if activity.new_data else {}
                
                # Create descriptions for changed fields
                descriptions: List[ActivityDescription] = []
                
                # If it's an UPDATE activity, show before/after values grouped together
                if activity.activity_type == "UPDATE" and old_data and new_data:
                    # Collect all changed fields
                    changed_fields: List[str] = []
                    before_values: List[str] = []
                    after_values: List[str] = []
                    
                    for field, new_value in new_data.items():
                        old_value = old_data.get(field, "")
                        if str(old_value) != str(new_value):
                            changed_fields.append(field)
                            before_values.append(str(old_value))
                            after_values.append(str(new_value))
                    
                    if changed_fields:
                        # Create grouped before and after descriptions
                        field_names = " / ".join(changed_fields)
                        before_vals = " / ".join(before_values)
                        after_vals = " / ".join(after_values)
                        
                        descriptions.extend([
                            ActivityDescription(
                                field=f"Talent / {field_names}",
                                value=before_vals,
                                label="Before"
                            ),
                            ActivityDescription(
                                field=f"Talent / {field_names}",
                                value=after_vals,
                                label="After"
                            )
                        ])
                elif activity.activity_type == "CREATE" and new_data:
                    # For CREATE activities, show the new values grouped
                    field_names = " / ".join(new_data.keys())
                    field_values = " / ".join(str(value) for value in new_data.values())
                    
                    descriptions.append(
                        ActivityDescription(
                            field=f"Talent / {field_names}:",
                            value=field_values,
                            label="Created"
                        )
                    )

                user = self.user_repository.get_by_id(activity.user_id)
                if user and user.role_id:
                    role = self.role_repository.get_by_id(user.role_id)
                else:
                    role = None
                
                # Format the activity log response
                formatted_activity = ActivityLogResponse(
                    date=activity.created_at.strftime("%m/%d/%Y") if activity.created_at else "",
                    time=activity.created_at.strftime("%H:%M") if activity.created_at else "",
                    event=activity.activity_type.title(),
                    user=user.name if user and user.name else "System User",
                    user_role=role.name if role else "Admin/System User",
                    descriptions=descriptions,
                    status="Succeeded" if activity.status else "Failed"
                )
                
                formatted_activities.append(formatted_activity)
            
            return GeneralResponse(
                message=f"Retrieved {len(formatted_activities)} formatted activity logs",
                status_code=200,
                data=formatted_activities
            )
            
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "talent_profile_id": talent_profile_id,
                    "skip": skip,
                    "limit": limit
                }
            )
            return GeneralResponse(
                message="An error occurred while retrieving formatted activity logs",
                status_code=500,
                data=[]
            )
