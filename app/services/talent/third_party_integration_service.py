"""Service layer for talent third party integration operations."""

from typing import List
from fastapi import Depends, Request

from app.repositories.talent.third_party_integration_repository import TalentThirdPartyIntegrationRepository
from app.schemas.talent.third_party_integration_schema import (
    TalentThirdPartyIntegrationCreate,
    TalentThirdPartyIntegrationUpdate,
    TalentThirdPartyIntegrationResponse
)
from app.core.activity_tracker import track_talent_activity_sync
from app.core.logs import log_error_with_context
from app.core.utils import get_changed_data
from app.core.jwt import get_current_user
from app.db.models import User


class TalentThirdPartyIntegrationService:
    """Service class for talent third party integration operations."""
    
    def __init__(
        self,
        repository: TalentThirdPartyIntegrationRepository = Depends(),
        current_user: User = Depends(get_current_user),
    ):
        """Initialize the service with dependencies."""
        self.repository = repository
        self.current_user = current_user
    
    def get_third_party_integrations_by_talent(self, talent_profile_id: int) -> List[TalentThirdPartyIntegrationResponse]:
        """Get all third party integrations for a specific talent profile."""
        try:
            integrations = self.repository.get_by_talent_profile_id(talent_profile_id)
            return [
                TalentThirdPartyIntegrationResponse.model_validate(integration)
                for integration in integrations
            ]
        except Exception as e:
          log_error_with_context(
                    error=e,
                    context={
                    "talent_profile_id": talent_profile_id,
                    "user_id": self.current_user.id
                })
          raise
    
    def create_third_party_integration(self, integration_data: TalentThirdPartyIntegrationCreate, request: Request) -> None:
        """Create a new third party integration."""
        try:
            # Check if integration already exists
            existing_integrations = self.repository.get_by_talent_profile_id(
                integration_data.talent_profile_id
            )
            
            for existing in existing_integrations:
                if existing.third_party == integration_data.third_party:
                    raise ValueError(
                        f"Third party integration for {integration_data.third_party} already exists for this talent"
                    )
            
            # Create the integration
            created_integration = self.repository.create(integration_data)
            
            # Track activity
            track_talent_activity_sync(
                talent_profile_id=created_integration.talent_profile_id,
                activity_type="CREATE",
                activity_description=f"Created third party integration: {created_integration.third_party}",
                request=request,
                current_user=self.current_user
        )
            
        except ValueError:
            raise
        except Exception as e:
            log_error_with_context(
                    error=e,
                    context={
                    "integration_data": integration_data.model_dump(),
                    "user_id": self.current_user.id
                })
            raise ValueError("Failed to create third party integration")
    
    def update_third_party_integration(self, integration_id: int, update_data: TalentThirdPartyIntegrationUpdate, request: Request) -> None:
        """Update an existing third party integration."""
        try:
            # Get existing integration
            existing_integration = self.repository.get_by_id(integration_id)
            if not existing_integration:
                raise ValueError(f"Third party integration with ID {integration_id} not found")
            
            # Get changed data for activity tracking
            old_data = existing_integration.model_dump()
            changed_data = get_changed_data(old_data, update_data.model_dump(exclude_unset=True))
            
            if not changed_data:
                return
            
            # Update the integration
            updated_integration = self.repository.update(integration_id, update_data)
            
            # Track activity
            if changed_data:
                track_talent_activity_sync(
                talent_profile_id=updated_integration.talent_profile_id,
                activity_type="UPDATE",
                activity_description=f"Updated third party integration: {updated_integration.third_party}",
                request=request,
                current_user=self.current_user
        )
        
        except ValueError:
            raise
        except Exception as e:
            log_error_with_context(
                    error=e,
                    context={
                    "integration_id": integration_id,
                    "update_data": update_data.model_dump(),
                    "user_id": self.current_user.id
                })
            raise
