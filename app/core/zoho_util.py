
import json
from requests import request

from app.core.config import settings

def gen_access_token(refresh_token: str):
    """Generate Zoho access token."""
    url = f"https://accounts.zoho.com/oauth/v2/token?refresh_token={refresh_token}&grant_type=refresh_token&client_id={settings.zoho_client_id}&client_secret={settings.zoho_client_secret}"

    req = request("POST", url)

    if 'error' in req.json():
        return req.json()['error']
    return req.json()['access_token']


def get_organization_id(refresh_token: str):
    """Get Zoho organization ID."""

    url = "https://mail.zoho.com/api/organization"

    access_token = gen_access_token(refresh_token)
    if 'error' in access_token:
        return access_token
    headers = {
        "Authorization": f"Zoho-oauthtoken {access_token}",
        "Content-Type": "application/json",
        "Accept": "application/json",
    }
    req = request("GET", url, headers=headers)
    if 'error' in req.json():
        return req.json()['error']
    return req.json()['data']['zoid']


def get_all_org_users(refresh_token: str):
    """Get all Zoho organization users."""
    url = f"https://mail.zoho.com/api/organization/{settings.zoho_org_id}/accounts?start=1&limit=1"
    access_token = gen_access_token(refresh_token)
    if 'error' in access_token:
        return access_token
    headers = {
        "Authorization": f"Zoho-oauthtoken {access_token}",
        "Content-Type": "application/json",
        "Accept": "application/json",
    }
    req = request("GET", url, headers=headers)
    if 'error' in req.json():
        return req.json()['error']
    print(json.dumps(req.json(), indent=4))
    return req.json()['data']


def add_user_to_org(user: dict[str, str]):
    """Add user to Zoho organization."""
    url = f"https://mail.zoho.com/api/organization/{settings.zoho_org_id}/accounts"

    access_token = gen_access_token(settings.zoho_organization_refresh_token)
    if 'error' in access_token:
        return access_token
    
    headers = {
        "Authorization": f"Zoho-oauthtoken {access_token}",
        "Content-Type": "application/json",
        "Accept": "application/json",
    }

    data: dict[str, str | bool] = {
      "primaryEmailAddress": user['email'],
      "password": "Asd@1234",
      "firstName": user['first_name'],
      "lastName": user['last_name'],
      "oneTimePassword": True,
      "role": user['role'],
      "timezone": user['timezone'],
    }

    req = request("POST", url, headers=headers, data=json.dumps(data))
    print(json.dumps(req.json(), indent=4))
    if 'error' in req.json():
        return req.json()['error']
    return req.json()['data']
