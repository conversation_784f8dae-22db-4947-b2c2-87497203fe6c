"""Talent API routers package."""

from .allergies_router import router as allergies_router
from .candidate_router import router as candidate_router
from .chronic_conditions_router import router as chronic_conditions_router
from .documents_router import router as documents_router
from .ongoing_health_router import router as ongoing_health_router
from .past_health_router import router as past_health_router
from .banking_router import router as banking_router
from .payroll_router import router as payroll_router
from .emergency_contact_router import router as emergency_contact_router
from .client_info_router import router as client_info_router
from .skill_set_router import router as skill_set_router
from .position_router import router as position_router
from .vacation_router import router as vacation_router
from .location_router import router as location_router
from .equipment_mapping_router import router as equipment_mapping_router
from .software_mapping_router import router as software_mapping_router
from .it_documents_router import router as it_documents_router
from .activity_router import router as activity_router

__all__ = [
    "allergies_router",
    "candidate_router",
    "chronic_conditions_router",
    "documents_router",
    "ongoing_health_router",
    "past_health_router",
    "banking_router",
    "payroll_router",
    "emergency_contact_router",
    "client_info_router",
    "skill_set_router",
    "position_router",
    "vacation_router",
    "location_router",
    "equipment_mapping_router",
    "software_mapping_router",
    "it_documents_router",
    "activity_router",
]
