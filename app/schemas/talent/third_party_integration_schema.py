"""Schemas for talent third party integration operations."""

from typing import Optional
from pydantic import BaseModel, Field
from datetime import datetime


class TalentThirdPartyIntegrationBase(BaseModel):
    """Base schema for talent third party integration."""
    
    talent_profile_id: int = Field(
        ..., 
        description="ID of the talent profile",
        gt=0
    )
    third_party: str = Field(
        ..., 
        description="Name of the third party platform",
        min_length=1,
        max_length=100
    )
    third_party_id: str = Field(
        ..., 
        description="ID on the third party platform",
        min_length=1,
        max_length=255
    )
    status: str = Field(
        default="active", 
        description="Status of the integration"
    )


class TalentThirdPartyIntegrationCreate(TalentThirdPartyIntegrationBase):
    """Schema for creating a new talent third party integration."""
    pass


class TalentThirdPartyIntegrationUpdate(BaseModel):
    """Schema for updating an existing talent third party integration."""
    
    third_party: Optional[str] = Field(
        None, 
        description="Name of the third party platform",
        min_length=1,
        max_length=100
    )
    third_party_id: Optional[str] = Field(
        None, 
        description="ID on the third party platform",
        min_length=1,
        max_length=255
    )
    status: Optional[str] = Field(
        None, 
        description="Status of the integration"
    )


class TalentThirdPartyIntegrationResponse(TalentThirdPartyIntegrationBase):
    """Schema for talent third party integration responses."""
    
    id: int = Field(
        ..., 
        description="Unique identifier for the third party integration"
    )
    created_at: datetime = Field(
        ..., 
        description="Timestamp when the integration was created"
    )
    updated_at: Optional[datetime] = Field(
        None, 
        description="Timestamp when the integration was last updated"
    )
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True
