import { GeneralResponse } from "@/types/talent"
import { 
  ThirdPartyIntegrationCreate, 
  ThirdPartyIntegrationUpdate, 
  ThirdPartyIntegrationResponse 
} from "@/types/third-party-integration"
import { convertKeysToCamelCase, convertKeysToSnakeCase } from "@/utils/case-converter"
import { getCallInstace, postCallInstace, putCallInstace } from "@/utils/http-handler"

const prefix = '/talent/third-party-integration'

export const thirdPartyIntegrationApi = {
  /**
   * Get all third party integrations for a specific talent
   */
  async getByTalentId(talentId: number): Promise<GeneralResponse<ThirdPartyIntegrationResponse[]>> {
    const response = await getCallInstace(`${prefix}/get-by-talent/${talentId}`)
    response.data = convertKeysToCamelCase(response.data)
    return response
  },

  /**
   * Create a new third party integration
   */
  async create(data: ThirdPartyIntegrationCreate): Promise<GeneralResponse<ThirdPartyIntegrationResponse>> {
    const snakeCaseData = convertKeysToSnakeCase(data)
    const response = await postCallInstace(`${prefix}/create`, snakeCaseData)
    response.data = convertKeysToCamelCase(response.data)
    return response
  },

  /**
   * Update an existing third party integration
   */
  async update(integrationId: number, data: ThirdPartyIntegrationUpdate): Promise<GeneralResponse<ThirdPartyIntegrationResponse>> {
    const snakeCaseData = convertKeysToSnakeCase(data)
    const response = await putCallInstace(`${prefix}/update/${integrationId}`, snakeCaseData)
    response.data = convertKeysToCamelCase(response.data)
    return response
  },

  /**
   * Delete a third party integration (sets status to not-created)
   */
  async delete(integrationId: number): Promise<GeneralResponse> {
    const response = await this.update(integrationId, { status: 'not-created' })
    return response
  },

  /**
   * Toggle integration status between active and inactive
   */
  async toggle(integrationId: number, currentStatus: string): Promise<GeneralResponse<ThirdPartyIntegrationResponse>> {
    const newStatus = currentStatus === 'active' ? 'inactive' : 'active'
    return await this.update(integrationId, { status: newStatus })
  }
}
