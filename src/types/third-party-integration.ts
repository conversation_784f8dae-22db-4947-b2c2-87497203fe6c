/**
 * Third Party Integration types for talent management
 */

export interface ThirdPartyIntegrationBase {
  talentProfileId: number
  thirdParty: string
  thirdPartyId: string
}

export interface ThirdPartyIntegrationCreate extends ThirdPartyIntegrationBase {}

export interface ThirdPartyIntegrationUpdate {
  thirdParty?: string
  thirdPartyId?: string
}

export interface ThirdPartyIntegrationResponse extends ThirdPartyIntegrationBase {
  id: number
  createdAt: string
  updatedAt?: string
}

export interface ThirdPartyIntegrationDisplay {
  id: number
  name: IntegrationType
  is_active: boolean
  description: string
  thirdPartyId?: string
  createdAt?: string
  updatedAt?: string
}

// Predefined integration types
export const INTEGRATION_TYPES = {
  ZOHO_MAIL: 'Zoho Mail',
  JIBBLE: 'Jibble',
  ACTIVTRAK: 'ActivTrak'
} as const

export type IntegrationType = typeof INTEGRATION_TYPES[keyof typeof INTEGRATION_TYPES]

// Integration descriptions
export const INTEGRATION_DESCRIPTIONS = {
  [INTEGRATION_TYPES.ZOHO_MAIL]: 'Email integration for communication',
  [INTEGRATION_TYPES.JIBBLE]: 'Time tracking and attendance management',
  [INTEGRATION_TYPES.ACTIVTRAK]: 'Employee monitoring and productivity tracking'
} as const
