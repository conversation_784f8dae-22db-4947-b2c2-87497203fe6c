<script setup lang="ts">
import { ref } from 'vue'
import { Plus, Power, Trash2 } from 'lucide-vue-next'

// Integration data
const integrations = ref([
  {
    id: 1,
    name: 'Zoho Mail',
    status: 'active', // active, inactive
    description: 'Email integration for communication'
  },
  {
    id: 2,
    name: 'Jibble',
    status: 'inactive',
    description: 'Time tracking and attendance management'
  },
  {
    id: 3,
    name: 'ActivTrak',
    status: 'active',
    description: 'Employee monitoring and productivity tracking'
  }
])

// Actions
const createIntegration = (integrationName: string) => {
  console.log(`Creating integration for ${integrationName}`)
  // TODO: Implement create integration logic
}

const toggleIntegration = (integration: any) => {
  integration.status = integration.status === 'active' ? 'inactive' : 'active'
  console.log(`${integration.status === 'active' ? 'Enabling' : 'Disabling'} ${integration.name}`)
  // TODO: Implement toggle integration logic
}

const deleteIntegration = (integrationName: string) => {
  console.log(`Deleting integration for ${integrationName}`)
  // TODO: Implement delete integration logic
}

const getStatusColor = (status: string) => {
  return status === 'active' ? 'text-green-600 bg-green-100' : 'text-red-600 bg-red-100'
}

const getStatusText = (status: string) => {
  return status === 'active' ? 'Active' : 'Inactive'
}
</script>

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
      <div>
        <h2 class="text-lg font-semibold text-gray-900">3rd Party Integrations</h2>
        <p class="text-sm text-gray-600 mt-1">Manage external service integrations for this talent</p>
      </div>
    </div>

    <!-- Integrations Table -->
    <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Service
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Description
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="integration in integrations" :key="integration.id" class="hover:bg-gray-50">
              <!-- Service Name -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">
                  {{ integration.name }}
                </div>
              </td>

              <!-- Description -->
              <td class="px-6 py-4">
                <div class="text-sm text-gray-600">
                  {{ integration.description }}
                </div>
              </td>

              <!-- Status -->
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="[
                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                  getStatusColor(integration.status)
                ]">
                  {{ getStatusText(integration.status) }}
                </span>
              </td>

              <!-- Actions -->
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex items-center space-x-3">
                  <!-- Create/Setup Button -->
                  <button
                    @click="createIntegration(integration.name)"
                    class="inline-flex items-center p-2 text-green-600 hover:text-green-900 hover:bg-green-50 rounded-md transition-colors"
                    title="Create/Setup Integration"
                  >
                    <Plus :size="16" />
                  </button>

                  <!-- Enable/Disable Toggle -->
                  <button
                    @click="toggleIntegration(integration)"
                    :class="[
                      'inline-flex items-center p-2 rounded-md transition-colors',
                      integration.status === 'active'
                        ? 'text-orange-600 hover:text-orange-900 hover:bg-orange-50'
                        : 'text-blue-600 hover:text-blue-900 hover:bg-blue-50'
                    ]"
                    :title="integration.status === 'active' ? 'Disable Integration' : 'Enable Integration'"
                  >
                    <Power :size="16" />
                  </button>

                  <!-- Delete Button -->
                  <button
                    @click="deleteIntegration(integration.name)"
                    class="inline-flex items-center p-2 text-red-600 hover:text-red-900 hover:bg-red-50 rounded-md transition-colors"
                    title="Delete Integration"
                  >
                    <Trash2 :size="16" />
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Empty State (if no integrations) -->
    <div v-if="integrations.length === 0" class="text-center py-12">
      <div class="text-gray-400 mb-4">
        <Plus :size="48" class="mx-auto" />
      </div>
      <h3 class="text-lg font-medium text-gray-900 mb-2">No Integrations</h3>
      <p class="text-gray-500">No third-party integrations have been configured for this talent.</p>
    </div>
  </div>
</template>
