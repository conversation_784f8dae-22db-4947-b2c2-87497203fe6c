<script setup lang="ts">
import { ref } from 'vue'
import { Plus, Power, Trash2 } from 'lucide-vue-next'

// Integration data
const integrations = ref([
  {
    id: 1,
    name: 'Zoho Mail',
    status: 'not-created', // not-created, active, inactive, disabled
    description: 'Email integration for communication'
  },
  {
    id: 2,
    name: 'Jibble',
    status: 'not-created',
    description: 'Time tracking and attendance management'
  },
  {
    id: 3,
    name: 'ActivTrak',
    status: 'not-created',
    description: 'Employee monitoring and productivity tracking'
  }
])

// Actions
const createIntegration = (integrationName: string) => {
  console.log(`Creating integration for ${integrationName}`)

  // Find the integration and update its status
  const integration = integrations.value.find(int => int.name === integrationName)
  if (integration) {
    integration.status = 'active' // Set to active after creation
    console.log(`${integrationName} integration created and activated`)
  }

  // TODO: Implement actual API call for create integration logic
}

const toggleIntegration = (integration: any) => {
  // Only allow toggle if integration is created (active/inactive)
  if (integration.status === 'active') {
    integration.status = 'inactive'
    console.log(`Disabling ${integration.name}`)
  } else if (integration.status === 'inactive') {
    integration.status = 'active'
    console.log(`Enabling ${integration.name}`)
  } else {
    console.log(`Cannot toggle ${integration.name} - integration not created yet`)
  }
  // TODO: Implement toggle integration logic
}

const deleteIntegration = (integrationName: string) => {
  console.log(`Deleting integration for ${integrationName}`)

  // Find the integration and reset its status
  const integration = integrations.value.find(int => int.name === integrationName)
  if (integration) {
    integration.status = 'not-created' // Reset to not-created after deletion
    console.log(`${integrationName} integration deleted and reset to not-created`)
  }

  // TODO: Implement actual API call for delete integration logic
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'active':
      return 'text-green-600 bg-green-100'
    case 'inactive':
      return 'text-orange-600 bg-orange-100'
    case 'disabled':
      return 'text-red-600 bg-red-100'
    case 'not-created':
    default:
      return 'text-gray-600 bg-gray-100'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'active':
      return 'Active'
    case 'inactive':
      return 'Inactive'
    case 'disabled':
      return 'Disabled'
    case 'not-created':
    default:
      return 'Not Created'
  }
}
</script>

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
      <div>
        <h2 class="text-lg font-semibold text-gray-900">3rd Party Integrations</h2>
        <p class="mt-1 text-sm text-gray-600">Manage external service integrations for this talent</p>
      </div>
    </div>

    <!-- Integrations Table -->
    <div class="overflow-hidden bg-white rounded-lg border border-gray-200">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                Service
              </th>
              <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                Description
              </th>
              <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                Status
              </th>
              <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="integration in integrations" :key="integration.id" class="hover:bg-gray-50">
              <!-- Service Name -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">
                  {{ integration.name }}
                </div>
              </td>

              <!-- Description -->
              <td class="px-6 py-4">
                <div class="text-sm text-gray-600">
                  {{ integration.description }}
                </div>
              </td>

              <!-- Status -->
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="[
                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                  getStatusColor(integration.status)
                ]">
                  {{ getStatusText(integration.status) }}
                </span>
              </td>

              <!-- Actions -->
              <td class="px-6 py-4 text-sm font-medium whitespace-nowrap">
                <div class="flex items-center space-x-3">
                  <!-- Create/Setup Button - Only show if not created -->
                  <button
                    v-if="integration.status === 'not-created'"
                    @click="createIntegration(integration.name)"
                    class="inline-flex items-center p-2 text-green-600 rounded-md transition-colors hover:text-green-900 hover:bg-green-50"
                    title="Create/Setup Integration"
                  >
                    <Plus :size="16" />
                  </button>

                  <!-- Enable/Disable Toggle - Only show if created (active/inactive) -->
                  <button
                    v-if="integration.status === 'active' || integration.status === 'inactive'"
                    @click="toggleIntegration(integration)"
                    :class="[
                      'inline-flex items-center p-2 rounded-md transition-colors',
                      integration.status === 'active'
                        ? 'text-orange-600 hover:text-orange-900 hover:bg-orange-50'
                        : 'text-blue-600 hover:text-blue-900 hover:bg-blue-50'
                    ]"
                    :title="integration.status === 'active' ? 'Disable Integration' : 'Enable Integration'"
                  >
                    <Power :size="16" />
                  </button>

                  <!-- Delete Button - Only show if created -->
                  <button
                    v-if="integration.status !== 'not-created'"
                    @click="deleteIntegration(integration.name)"
                    class="inline-flex items-center p-2 text-red-600 rounded-md transition-colors hover:text-red-900 hover:bg-red-50"
                    title="Delete Integration"
                  >
                    <Trash2 :size="16" />
                  </button>

                  <!-- Placeholder for not-created state -->
                  <div v-if="integration.status === 'not-created'" class="flex items-center space-x-3">
                    <!-- Disabled toggle button as visual indicator -->
                    <button
                      disabled
                      class="inline-flex items-center p-2 text-gray-400 rounded-md cursor-not-allowed"
                      title="Create integration first to enable/disable"
                    >
                      <Power :size="16" />
                    </button>
                    <!-- Disabled delete button as visual indicator -->
                    <button
                      disabled
                      class="inline-flex items-center p-2 text-gray-400 rounded-md cursor-not-allowed"
                      title="Create integration first to delete"
                    >
                      <Trash2 :size="16" />
                    </button>
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Empty State (if no integrations) -->
    <div v-if="integrations.length === 0" class="py-12 text-center">
      <div class="mb-4 text-gray-400">
        <Plus :size="48" class="mx-auto" />
      </div>
      <h3 class="mb-2 text-lg font-medium text-gray-900">No Integrations</h3>
      <p class="text-gray-500">No third-party integrations have been configured for this talent.</p>
    </div>
  </div>
</template>
