import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { toast } from 'vue3-toastify'
import { thirdPartyIntegrationApi } from '@/apis/talent/third-party-integration'
import { 
  ThirdPartyIntegrationDisplay, 
  ThirdPartyIntegrationResponse,
  INTEGRATION_TYPES,
  INTEGRATION_DESCRIPTIONS,
  type IntegrationType
} from '@/types/third-party-integration'

export const useThirdPartyIntegrations = () => {
  const route = useRoute()
  
  // State
  const integrations = ref<ThirdPartyIntegrationDisplay[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Computed
  const talentId = computed(() => {
    const id = route.params.id as string
    return id ? Number(id) : 0
  })

  // Helper function to convert API response to display format
  const convertToDisplayFormat = (apiIntegrations: ThirdPartyIntegrationResponse[]): ThirdPartyIntegrationDisplay[] => {
    const predefinedIntegrations: ThirdPartyIntegrationDisplay[] = Object.values(INTEGRATION_TYPES).map(name => ({
      id: 0, // Will be updated if integration exists
      name,
      status: 'not-created' as const,
      description: INTEGRATION_DESCRIPTIONS[name]
    }))

    // Update with actual data from API
    apiIntegrations.forEach(apiIntegration => {
      const index = predefinedIntegrations.findIndex(p => p.name === apiIntegration.thirdParty)
      if (index !== -1) {
        predefinedIntegrations[index].id = apiIntegration.id
        predefinedIntegrations[index].name = apiIntegration.thirdParty as IntegrationType
        predefinedIntegrations[index].description = INTEGRATION_DESCRIPTIONS[apiIntegration.thirdParty as IntegrationType] || 'Third party integration'
        predefinedIntegrations[index].thirdPartyId = apiIntegration.thirdPartyId
        predefinedIntegrations[index].createdAt = apiIntegration.createdAt
        predefinedIntegrations[index].updatedAt = apiIntegration.updatedAt
      }
    })

    return predefinedIntegrations
  }

  // Actions
  const fetchIntegrations = async () => {
    if (!talentId.value) {
      error.value = 'No talent ID provided'
      return
    }

    try {
      isLoading.value = true
      error.value = null
      
      const response = await thirdPartyIntegrationApi.getByTalentId(talentId.value)
      integrations.value = convertToDisplayFormat(response.data || [])
    } catch (err) {
      error.value = 'Failed to fetch third party integrations'
      console.error('Failed to fetch integrations:', err)
      toast.error('Failed to load integrations')
    } finally {
      isLoading.value = false
    }
  }

  const createIntegration = async (integrationName: string) => {
    if (!talentId.value) {
      toast.error('No talent ID provided')
      return
    }

    try {
      isLoading.value = true
      
      // Generate a default third party ID (can be customized later)
      const thirdPartyId = `${integrationName.toLowerCase().replace(/\s+/g, '_')}_${talentId.value}_${Date.now()}`
      
      await thirdPartyIntegrationApi.create({
        talentProfileId: talentId.value,
        thirdParty: integrationName,
        thirdPartyId,
      })

      toast.success(`${integrationName} integration created successfully`)
      await fetchIntegrations() // Refresh the list
    } catch (err) {
      console.error('Failed to create integration:', err)
      toast.error(`Failed to create ${integrationName} integration`)
    } finally {
      isLoading.value = false
    }
  }

  const toggleIntegration = async (integration: ThirdPartyIntegrationDisplay) => {
    if (integration.status === 'not-created') {
      toast.warning('Please create the integration first')
      return
    }

    try {
      isLoading.value = true
      
      await thirdPartyIntegrationApi.toggle(integration.id, integration.status)
      
      const newStatus = integration.status === 'active' ? 'inactive' : 'active'
      const action = newStatus === 'active' ? 'enabled' : 'disabled'
      
      toast.success(`${integration.name} integration ${action}`)
      await fetchIntegrations() // Refresh the list
    } catch (err) {
      console.error('Failed to toggle integration:', err)
      toast.error(`Failed to toggle ${integration.name} integration`)
    } finally {
      isLoading.value = false
    }
  }

  const deleteIntegration = async (integrationName: string) => {
    const integration = integrations.value.find(i => i.name === integrationName)
    if (!integration || integration.status === 'not-created') {
      toast.warning('Integration not found or not created')
      return
    }

    try {
      isLoading.value = true
      
      await thirdPartyIntegrationApi.delete(integration.id)
      
      toast.success(`${integrationName} integration deleted successfully`)
      await fetchIntegrations() // Refresh the list
    } catch (err) {
      console.error('Failed to delete integration:', err)
      toast.error(`Failed to delete ${integrationName} integration`)
    } finally {
      isLoading.value = false
    }
  }

  // Status helpers
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-green-600 bg-green-100'
      case 'inactive':
        return 'text-orange-600 bg-orange-100'
      case 'disabled':
        return 'text-red-600 bg-red-100'
      case 'not-created':
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Active'
      case 'inactive':
        return 'Inactive'
      case 'disabled':
        return 'Disabled'
      case 'not-created':
      default:
        return 'Not Created'
    }
  }

  return {
    // State
    integrations,
    isLoading,
    error,
    
    // Computed
    talentId,
    
    // Actions
    fetchIntegrations,
    createIntegration,
    toggleIntegration,
    deleteIntegration,
    
    // Helpers
    getStatusColor,
    getStatusText
  }
}
